#management:
#  server:
#    # do not change port
#    port: 9193
#
#  endpoints:
#    exposure:
#      include: health
#
#  endpoint:
#    health:
#      probes:
#        enabled: true

spring:
  application:
    name: dh-spm-integration-service

  datasource:
    #Master schema
    master:
      hikari:
        driver-class-name: org.postgresql.Driver
        jdbc-url: ***********************************************************************************************
        username: "datahub.postgres"
        password: BCk9OwVKOLqweMl1WF9k3surBbVovetMzfjGXQQNxcqzNeyAQ4dRzLtdWmhDZbgp
        enabled: true
        maximum-pool-size: 10
        minimum-idle: 5
        idle-timeout: 10000 #10 seconds
        connection-timeout: 30000 #30 seconds
        pool-name: master-pool
    #Agency schema
    agency:
      hikari:
        driver-class-name: org.postgresql.Driver
        jdbc-url: ***********************************************************************************************
        username: "datahub.postgres"
        password: BCk9OwVKOLqweMl1WF9k3surBbVovetMzfjGXQQNxcqzNeyAQ4dRzLtdWmhDZbgp
        enabled: true
        maximum-pool-size: 30
        minimum-idle: 5
        idle-timeout: 10000 #10 seconds
        connection-timeout: 30000 #30 seconds
        pool-name: agency-pool

  jpa:
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        naming:
          physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
        show_sql: true
        format_sql: true
        use_sql_comments: true
        jdbc:
          time_zone: UTC
    open-in-view: false
  # redis
  data:
    redis:
      host: **************
      port: 6379

aws:
  region: ap-southeast-2
  access-key: test
  secret-key: test
  bucket-name: datahub-storage-us-test

cache:
  config:
    cache-prefix: "[DATAHUB]"
    default-ttl-minutes: 60

server:
  port: 8081
  servlet:
    context-path: /integration
