package com.yunex.datahub.integration.controller;

import com.yunex.datahub.integration.dto.AgencyResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@Tag(name = "Agency", description = "Agency Resources")
@RequestMapping("/internal/agencies")
public interface AgencyResourceV2 {

    @Operation(summary = "Retrieve a list of agencies.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of agency.",
                    content = { @Content(mediaType = "application/json", examples = {
                            @ExampleObject(value = """
                                    {}
                                    """)
                    }) }),
            @ApiResponse(responseCode = "400", description = "Bad request. E.g., agency does not exist") })
    @GetMapping("/provisioned")
    ResponseEntity<List<AgencyResponseDto>> getProvisionedAgencies();

    @Operation(summary = "Retrieve a provisioned agency by agency ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "provisioned agency.",
                    content = { @Content(mediaType = "application/json", examples = {
                            @ExampleObject(value = """
                                    {}
                                    """)
                    }) }),
            @ApiResponse(responseCode = "400", description = "Bad request. E.g., agency does not exist") })
    @GetMapping("/{agency_id}")
    ResponseEntity<AgencyResponseDto> getAgencyById(@PathVariable("agency_id") Integer agencyId);

}
