package com.yunex.datahub.integration.controller;

import com.yunex.datahub.integration.converter.AgencySchemaConverter;
import com.yunex.datahub.integration.dto.AgencyResponseDto;
import com.yunex.datahub.integration.service.AgencySchemaService;
import com.yunex.datahub.multitenant.entity.AgencySchema;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 8/5/2025
 **/
@RestController
@RequiredArgsConstructor
public class AgencyController implements AgencyResourceV2 {

    private final AgencySchemaService agencySchemaService;

    @Override
    public ResponseEntity<List<AgencyResponseDto>> getProvisionedAgencies() {
        return ResponseEntity.ok(agencySchemaService.findProvisionedAgencies().stream()
                .map(agencySchema -> AgencyResponseDto.builder()
                        .agencyId(agencySchema.getId())
                        .agencyName(agencySchema.getAgencyName())
                        .active(agencySchema.getActivated())
                        .schema(agencySchema.getSchema())
                        .createdAt(agencySchema.getCreatedAt())
                        .lastModifiedAt(agencySchema.getLastModifiedAt())
                        .timezoneId(agencySchema.getTimeZoneId())
                        .build())
                .toList());
    }

    @Override
    public ResponseEntity<AgencyResponseDto> getAgencyById(Integer agencyId) {
        AgencySchema agencySchema = agencySchemaService.getAgencySchemaById(agencyId);
        return ResponseEntity.ok(AgencySchemaConverter.toDTO(agencySchema));
    }

}
