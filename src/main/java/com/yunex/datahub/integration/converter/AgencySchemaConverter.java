package com.yunex.datahub.integration.converter;

import com.yunex.datahub.integration.dto.AgencyResponseDto;
import com.yunex.datahub.multitenant.entity.AgencySchema;

public final class AgencySchemaConverter {

    private AgencySchemaConverter() {
        // Private constructor to prevent instantiation
    }

    public static AgencyResponseDto toDTO(AgencySchema entity) {
        if (entity == null) {
            return null;
        }
        return AgencyResponseDto.builder()
                .agencyId(entity.getId())
                .agencyName(entity.getAgencyName())
                .active(entity.getActivated())
                .schema(entity.getSchema())
                .createdAt(entity.getCreatedAt())
                .lastModifiedAt(entity.getLastModifiedAt())
                .timezoneId(entity.getTimeZoneId())
                .build();
    }

}