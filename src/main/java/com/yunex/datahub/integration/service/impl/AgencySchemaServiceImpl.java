package com.yunex.datahub.integration.service.impl;

import com.yunex.datahub.integration.config.CacheConfig;
import com.yunex.datahub.integration.service.AgencySchemaService;
import com.yunex.datahub.multitenant.entity.AgencySchema;
import com.yunex.datahub.multitenant.repository.AgencySchemaRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.yunex.datahub.multitenant.config.MasterPersistentConfig.MASTER_TRANSACTION_MANAGER;

@Slf4j
@Service
@Transactional(transactionManager = MASTER_TRANSACTION_MANAGER)
public class AgencySchemaServiceImpl implements AgencySchemaService {

    private final AgencySchemaRepository agencySchemaRepository;

    public AgencySchemaServiceImpl(AgencySchemaRepository agencySchemaRepository) {
        this.agencySchemaRepository = agencySchemaRepository;
    }

    @Override
    public List<AgencySchema> findProvisionedAgencies() {
        return agencySchemaRepository.findProvisionedSchemas();
    }

    @Cacheable(value = CacheConfig.AGENCY_ENTITY_CACHE, key = "#agencyId")
    @Override
    public AgencySchema getAgencySchemaById(Integer agencyId) {
        log.info("Fetching agency schema for agencyId: {}", agencyId);
        return agencySchemaRepository.findById(agencyId).orElseThrow(() -> new RuntimeException("Agency not found"));
    }

}