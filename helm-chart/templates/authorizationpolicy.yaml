apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: {{ .Chart.Name }}
spec:
  selector:
    matchLabels:
      app: {{ .Chart.Name }}
  action: ALLOW
  rules:
    # Allow access to all endpoints without authentication in the internal network
    - from:
        - source:
            namespaces: [ "insights" ]
      to:
        - operation:
            paths: [ "*" ]
    # Allow access from clients with role 646 over the internet
    - from:
        - source:
            requestPrincipals: [ "*" ]
      to:
        - operation:
            paths: [ "*" ]
      when:
        - key: 'request.auth.claims[aud]'
          values:
            {{- range .Values.authorizationPolicies.allowedPrincipals }}
            - {{ . }}
            {{- end }}