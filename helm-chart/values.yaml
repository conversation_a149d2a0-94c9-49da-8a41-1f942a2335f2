# Application hostname and networking settings
global:
  baseDomain: us.dev.yunextraffic.cloud
subdomain: datahub
contextPath: /integration
clusterDomain: svc.cluster.local
service:
  type: ClusterIP
  port: 80
nginx:
  port: 8080

container:
  javaOptions: -XX:+UseG1GC -XX:MaxMetaspaceSize=512m -XX:MaxRAMPercentage=85.0

# Replication and Autoscaling
replicaCount: 1
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

namespace: datahub

# Container Image source
image:
  repository: tactics-registry.artifactory.mocca.yunextraffic.cloud
  pullPolicy: Always
imagePullSecrets:
  - name: tactics-registry.artifactory.mocca.yunextraffic.cloud

# Container security settings
podSecurityContext: { }
securityContext: { }

# Container resources settings
resources:
  limits:
    memory: "3Gi"
  requests:
    cpu: "300m"
    memory: "1Gi"

authorizationPolicies:
  allowedPrincipals: [ "'gatekeeper'", "'oauth2-gatekeeper'" ]

envfrompgsecrets:
  PG_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: datahub.postgres.postgres-cred
        key: password
envfromsecrets:
  REDIS_PASSWORD:
    valueFrom:
      secretKeyRef:
        name: datahub--central-redis-credentials
        key: auth

application:
  # Port configuration
  server:
    port: 8080
    servlet:
      context-path: /integration
    max-http-request-header-size: 100KB

  spring:
    datasource:
      #Master schema
      master:
        hikari:
          driver-class-name: org.postgresql.Driver
          jdbc-url: ***********************************************************************************************************************
          username: "datahub.postgres"
          # password: ENV inject
          enabled: true
          maximum-pool-size: 10
          minimum-idle: 5
          idle-timeout: 10000 #10 seconds
          connection-timeout: 30000 #30 seconds
          pool-name: master-pool
      #Agency schema
      agency:
        hikari:
          driver-class-name: org.postgresql.Driver
          jdbc-url: ***********************************************************************************************************************
          username: "datahub.postgres"
          # password: ENV inject
          enabled: true
          maximum-pool-size: 10
          minimum-idle: 5
          idle-timeout: 10000 #10 seconds
          connection-timeout: 30000 #30 seconds
          pool-name: agency-pool
    jpa:
      hibernate:
        ddl-auto: none
      properties:
        hibernate:
          dialect: org.hibernate.dialect.PostgreSQLDialect
          naming:
            physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
          show_sql: false
          format_sql: true
          use_sql_comments: true
          jdbc:
            time_zone: UTC
      open-in-view: false
    # redis
    data:
      redis:
        host: redis-master.studio
        port: 6379
        password: # ENV inject
  aws:
    region: us-east-1
    bucket-name: datahub-storage-us-dev

  cache:
    config:
      cache-prefix: "[DATAHUB]"
      default-ttl-minutes: 60